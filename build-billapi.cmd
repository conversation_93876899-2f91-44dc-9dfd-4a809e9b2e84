@echo off
echo Building billapi with Java 1.7...

set JAVA_HOME=C:\Work\java\jdk\jdk1.7.0_06
set PATH=%JAVA_HOME%\bin;%PATH%

echo Java version:
javac -version

echo Creating directories...
if not exist build\classes mkdir build\classes
if not exist output mkdir output

echo Cleaning build directory...
if exist build\classes\* del /q /s build\classes\*

echo Building classpath...
set CLASSPATH=classes;.
for %%f in (webapp\WEB-INF\lib\*.jar) do set CLASSPATH=!CLASSPATH!;%%f

echo Compiling billapi source files...
javac -cp "%CLASSPATH%" -d build\classes -encoding UTF-8 -source 1.7 -target 1.7 billapi\src\com\aisino\a6\business\util\*.java

if %ERRORLEVEL% neq 0 (
    echo Compilation failed for util classes
    goto :error
)

echo Copying resource files...
if exist billapi\resource (
    xcopy billapi\resource\* build\classes\ /s /e /y
)

echo Creating JAR file...
jar cf output\Aisino-A6-billapi-7.1.jar -C build\classes .

if %ERRORLEVEL% neq 0 (
    echo JAR creation failed
    goto :error
)

echo Build successful!
echo JAR file: output\Aisino-A6-billapi-7.1.jar
dir output\Aisino-A6-billapi-7.1.jar

echo JAR contents:
jar tf output\Aisino-A6-billapi-7.1.jar

goto :end

:error
echo Build failed!
exit /b 1

:end
echo Build completed!
