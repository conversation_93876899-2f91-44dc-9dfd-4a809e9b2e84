# Build billapi module with Java 1.7 - English output to avoid encoding issues
Write-Host "Building billapi module with Java 1.7..." -ForegroundColor Green

$javaHome = "C:\Work\java\jdk\jdk1.7.0_06"
$javac = "$javaHome\bin\javac.exe"
$jar = "$javaHome\bin\jar.exe"

# Set environment variables
$env:JAVA_HOME = $javaHome
$env:PATH = "$javaHome\bin;$env:PATH"

# Create build directory
$buildDir = "build\classes\billapi"
if (!(Test-Path $buildDir)) {
    New-Item -ItemType Directory -Path $buildDir -Force | Out-Null
}

# Clean build directory
Remove-Item -Path "$buildDir\*" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "Finding billapi Java source files..." -ForegroundColor Yellow
$javaFiles = Get-ChildItem -Path "billapi\src" -Filter "*.java" -Recurse

Write-Host "Found $($javaFiles.Count) Java files" -ForegroundColor Cyan

# Build classpath - include compiled classes and JAR files
Write-Host "Building classpath..." -ForegroundColor Yellow
$classpath = "classes;."

# Add all JAR files from webapp/WEB-INF/lib
$libDir = "webapp\WEB-INF\lib"
if (Test-Path $libDir) {
    $jarFiles = Get-ChildItem -Path $libDir -Filter "*.jar"
    foreach ($jar in $jarFiles) {
        $classpath += ";$($jar.FullName)"
    }
    Write-Host "Added $($jarFiles.Count) JAR files to classpath" -ForegroundColor Cyan
}

# Compile billapi files, ignore some errors
Write-Host "Starting compilation of billapi..." -ForegroundColor Yellow
$sourceFiles = $javaFiles | ForEach-Object { "`"$($_.FullName)`"" }

Write-Host "Compiling $($javaFiles.Count) Java files..." -ForegroundColor Gray
& $javac -cp $classpath -d $buildDir -encoding UTF-8 -source 1.7 -target 1.7 -Xlint:none $sourceFiles

# Check if any class files were compiled successfully
$classFiles = Get-ChildItem -Path $buildDir -Filter "*.class" -Recurse
Write-Host "Compilation generated $($classFiles.Count) class files" -ForegroundColor Cyan

if ($classFiles.Count -gt 0) {
    Write-Host "Partial compilation successful, creating JAR..." -ForegroundColor Yellow
    
    # Copy resource files
    $resourceDir = "billapi\resource"
    if (Test-Path $resourceDir) {
        Write-Host "Copying resource files..." -ForegroundColor Yellow
        Copy-Item -Path "$resourceDir\*" -Destination $buildDir -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    # Create JAR
    Write-Host "Creating JAR file..." -ForegroundColor Yellow
    $jarPath = "output\Aisino-A6-billapi-7.1.jar"
    
    # Ensure output directory exists
    if (!(Test-Path "output")) {
        New-Item -ItemType Directory -Path "output" -Force | Out-Null
    }
    
    # Create manifest file
    $manifestContent = @"
Manifest-Version: 1.0
Built-By: $env:USERNAME
Built-Date: $(Get-Date -Format "yyyyMMdd.HHmm")
Implementation-Title: billapi
Implementation-Version: 7.1
Java-Version: 1.7

"@
    
    $manifestFile = "build\MANIFEST.MF"
    Set-Content -Path $manifestFile -Value $manifestContent -Encoding UTF8
    
    & $jar cfm $jarPath $manifestFile -C $buildDir .
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "JAR file created successfully: $jarPath" -ForegroundColor Green
        
        if (Test-Path $jarPath) {
            $jarInfo = Get-Item $jarPath
            Write-Host "File size: $($jarInfo.Length) bytes" -ForegroundColor Cyan
            Write-Host "Created: $($jarInfo.CreationTime)" -ForegroundColor Cyan
            
            # Show JAR contents
            Write-Host "JAR file contents:" -ForegroundColor Yellow
            & $jar tf $jarPath | Select-Object -First 20
            $totalFiles = (& $jar tf $jarPath | Measure-Object).Count
            if ($totalFiles -gt 20) {
                Write-Host "... (and $($totalFiles - 20) more files)" -ForegroundColor Gray
            }
            Write-Host "Total files in JAR: $totalFiles" -ForegroundColor Cyan
        }
    } else {
        Write-Host "JAR creation failed" -ForegroundColor Red
    }
} else {
    Write-Host "No class files were compiled successfully, cannot create JAR" -ForegroundColor Red
    Write-Host "This might be due to missing dependencies" -ForegroundColor Yellow
}

Write-Host "Build process completed!" -ForegroundColor Green
