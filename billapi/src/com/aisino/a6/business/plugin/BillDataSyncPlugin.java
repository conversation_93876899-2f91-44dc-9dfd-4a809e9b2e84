package com.aisino.a6.business.plugin;

import com.aisino.a6.business.util.LoginAndLogout;
import com.aisino.a6Common.busutil.MaterialFreeNameInfo;
import com.aisino.aos.bill.common.BillUtils;
import com.aisino.aos.bill.plugin.BillCheckPlugin;
import com.aisino.aos.bill.vo.BillSetting;
import com.aisino.aos.preference.common.Prefer;
import com.aisino.aos.system.SessionHelper;
import com.aisino.app.a6.web.dotasklist.common.plugin.PuCheckPlugin;
import com.aisino.app.a6.web.dotasklist.common.util.DealBillFunctionUtil;
import com.aisino.app.web.dotasklist.common.util.AppendixUtil;
import com.aisino.app.web.dotasklist.daiban.plugin.DaibanUtil;
import com.aisino.platform.core.Guid;
import com.aisino.platform.core.MS;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.CsUtil;
import com.aisino.platform.util.MapUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;
import com.aisino.platform.view.listener.FormCreateListener;
import com.alibaba.druid.util.Base64;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.LogManager;

    /**
    * @ClassName: BillDataSyncPlugin
    * @Description: TODO(同步单据数据接口)
    * <AUTHOR>
    * @date 2025年8月6日
    *
    */
    
public class BillDataSyncPlugin implements FormCreateListener {
	private static final org.apache.log4j.Logger logger = LogManager.getLogger(BillDataSyncPlugin.class.getName());
	@Override
	public void onFormCreate(AbstractForm form, DataMsgBus bus) {
		
		logger.info("同步单据数据接口开始,参数alldata:"+bus.getString("alldata"));
		String alldata = bus.getString("alldata");
		alldata=getDecodeBase64(alldata);
		logger.info("同步单据数据接口开始,参数alldata解码后:"+alldata);
		bus.put("alldata", alldata);
		Map data = CsUtil.unserializeJson(alldata);
		LoginAndLogout l=new LoginAndLogout();
		l.login(CollectionUtil.getStringFromMap(data, "DBName"));
		try {
			String billtype = MapUtil.getString(data, "billtype");
			if ("PU_Order".equals(billtype)) {//采购订单
				PuOrderBillDataSync dataSync = new PuOrderBillDataSync();
				dataSync.billDataSync(form, bus);
			}else if ("SA_Order".equals(billtype)) {//销售订单
				SaOrderBillDataSync dataSync = new SaOrderBillDataSync();
				dataSync.billDataSync(form, bus);
			}else if ("PU_StkRecord".equals(billtype)) {//入库单
				PuStkRecordBillDataSync dataSync = new PuStkRecordBillDataSync();
				dataSync.billDataSync(form, bus);
			}else if ("SA_StkRecord".equals(billtype)) {//出库单
				SaStkRecordBillDataSync dataSync = new SaStkRecordBillDataSync();
				dataSync.billDataSync(form, bus);
			}
			l.logout();
		} catch (Exception e) {
			// TODO: handle exception
			Map map = new HashMap();
			map.put("errmsg", e.getMessage());
			map.put("status", 0);
			map.put("billcode", null);
			form.setReturn(map);
			l.logout();
			e.printStackTrace();
			return;
		}
	}
	public String getDecodeBase64(String str){
        byte[] b = null;
        String result = null;
        if(str != null){
            try {
            	byte[] decodedBytes = Base64.base64ToByteArray(str);
            	result = new String(decodedBytes,"utf-8");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }
}
