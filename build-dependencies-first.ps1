# Build dependencies first, then billapi
Write-Host "Building dependencies and billapi with Java 1.7..." -ForegroundColor Green

$javaHome = "C:\Work\java\jdk\jdk1.7.0_06"
$javac = "$javaHome\bin\javac.exe"
$jar = "$javaHome\bin\jar.exe"

# Set environment variables
$env:JAVA_HOME = $javaHome
$env:PATH = "$javaHome\bin;$env:PATH"

# Create build directory
$buildDir = "build\classes"
if (!(Test-Path $buildDir)) {
    New-Item -ItemType Directory -Path $buildDir -Force | Out-Null
}

# Clean build directory
Remove-Item -Path "$buildDir\*" -Recurse -Force -ErrorAction SilentlyContinue

# Build classpath
Write-Host "Building classpath..." -ForegroundColor Yellow
$classpath = "classes;."

# Add all JAR files from webapp/WEB-INF/lib
$libDir = "webapp\WEB-INF\lib"
if (Test-Path $libDir) {
    $jarFiles = Get-ChildItem -Path $libDir -Filter "*.jar"
    foreach ($jar in $jarFiles) {
        $classpath += ";$($jar.FullName)"
    }
    Write-Host "Added $($jarFiles.Count) JAR files to classpath" -ForegroundColor Cyan
}

# Step 1: Compile specific dependency files first
Write-Host "Step 1: Compiling dependency classes..." -ForegroundColor Yellow

$dependencyFiles = @(
    "A6COMMON\src\com\aisino\a6Common\busutil\MaterialFreeNameInfo.java",
    "SA\util\src\com\aisino\a6\business\sa\util\plugin\SaBusinessUtil.java"
)

$foundDeps = @()
foreach ($depFile in $dependencyFiles) {
    if (Test-Path $depFile) {
        $foundDeps += $depFile
        Write-Host "Found dependency: $depFile" -ForegroundColor Gray
    } else {
        Write-Host "Missing dependency: $depFile" -ForegroundColor Red
    }
}

if ($foundDeps.Count -gt 0) {
    Write-Host "Compiling $($foundDeps.Count) dependency files..." -ForegroundColor Yellow
    
    # Try to compile dependencies with relaxed error handling
    foreach ($depFile in $foundDeps) {
        Write-Host "Compiling: $depFile" -ForegroundColor Gray
        & $javac -cp $classpath -d $buildDir -encoding UTF-8 -source 1.7 -target 1.7 -Xlint:none "$depFile" 2>$null
    }
    
    # Check what was compiled
    $depClassFiles = Get-ChildItem -Path $buildDir -Filter "*.class" -Recurse
    Write-Host "Dependency compilation generated $($depClassFiles.Count) class files" -ForegroundColor Cyan
    
    # Update classpath to include compiled dependencies
    $classpath += ";$buildDir"
}

# Step 2: Compile billapi files
Write-Host "Step 2: Compiling billapi files..." -ForegroundColor Yellow

$billapiFiles = Get-ChildItem -Path "billapi\src" -Filter "*.java" -Recurse
Write-Host "Found $($billapiFiles.Count) billapi Java files" -ForegroundColor Cyan

$billapiBuildDir = "$buildDir\billapi"
if (!(Test-Path $billapiBuildDir)) {
    New-Item -ItemType Directory -Path $billapiBuildDir -Force | Out-Null
}

# Compile billapi files one by one to see which ones succeed
$successfulFiles = @()
foreach ($file in $billapiFiles) {
    Write-Host "Compiling: $($file.Name)" -ForegroundColor Gray
    $result = & $javac -cp $classpath -d $billapiBuildDir -encoding UTF-8 -source 1.7 -target 1.7 -Xlint:none "$($file.FullName)" 2>&1
    if ($LASTEXITCODE -eq 0) {
        $successfulFiles += $file
        Write-Host "  SUCCESS" -ForegroundColor Green
    } else {
        Write-Host "  FAILED" -ForegroundColor Red
    }
}

Write-Host "Successfully compiled $($successfulFiles.Count) out of $($billapiFiles.Count) billapi files" -ForegroundColor Cyan

# Check final class files
$finalClassFiles = Get-ChildItem -Path $billapiBuildDir -Filter "*.class" -Recurse
Write-Host "Total billapi class files generated: $($finalClassFiles.Count)" -ForegroundColor Cyan

if ($finalClassFiles.Count -gt 0) {
    Write-Host "Creating JAR with available classes..." -ForegroundColor Yellow
    
    # Copy resource files
    $resourceDir = "billapi\resource"
    if (Test-Path $resourceDir) {
        Write-Host "Copying resource files..." -ForegroundColor Yellow
        Copy-Item -Path "$resourceDir\*" -Destination $billapiBuildDir -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    # Create JAR
    $jarPath = "output\Aisino-A6-billapi-7.1.jar"
    
    # Ensure output directory exists
    if (!(Test-Path "output")) {
        New-Item -ItemType Directory -Path "output" -Force | Out-Null
    }
    
    # Create manifest file
    $manifestContent = @"
Manifest-Version: 1.0
Built-By: $env:USERNAME
Built-Date: $(Get-Date -Format "yyyyMMdd.HHmm")
Implementation-Title: billapi
Implementation-Version: 7.1
Java-Version: 1.7

"@
    
    $manifestFile = "build\MANIFEST.MF"
    Set-Content -Path $manifestFile -Value $manifestContent -Encoding UTF8
    
    & $jar cfm $jarPath $manifestFile -C $billapiBuildDir .
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "JAR file created successfully: $jarPath" -ForegroundColor Green
        
        if (Test-Path $jarPath) {
            $jarInfo = Get-Item $jarPath
            Write-Host "File size: $($jarInfo.Length) bytes" -ForegroundColor Cyan
            Write-Host "Created: $($jarInfo.CreationTime)" -ForegroundColor Cyan
            
            # Show JAR contents
            Write-Host "JAR file contents:" -ForegroundColor Yellow
            $jarContents = & $jar tf $jarPath
            $jarContents | Select-Object -First 15
            if ($jarContents.Count -gt 15) {
                Write-Host "... (and $($jarContents.Count - 15) more files)" -ForegroundColor Gray
            }
            Write-Host "Total files in JAR: $($jarContents.Count)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "JAR creation failed" -ForegroundColor Red
    }
} else {
    Write-Host "No class files were compiled successfully" -ForegroundColor Red
}

Write-Host "Build process completed!" -ForegroundColor Green
